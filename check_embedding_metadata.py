#!/usr/bin/env python3
"""
检查embedding_metadata表的结构和内容
"""
import sqlite3
import json
from pathlib import Path

def check_embedding_metadata():
    """检查embedding_metadata表"""
    db_path = Path("storage/chroma.sqlite3")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查看表结构
        print("📋 embedding_metadata表结构:")
        cursor.execute("PRAGMA table_info(embedding_metadata)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        print("\n" + "="*60)
        
        # 2. 查看总记录数
        cursor.execute("SELECT COUNT(*) FROM embedding_metadata")
        total_count = cursor.fetchone()[0]
        print(f"📊 总记录数: {total_count}")
        
        # 3. 查看前几条记录的结构
        print("\n🔍 前3条记录的_node_content结构:")
        cursor.execute("SELECT id, key, string_value FROM embedding_metadata WHERE key = '_node_content' LIMIT 3")
        records = cursor.fetchall()
        
        for i, (record_id, key, content) in enumerate(records, 1):
            print(f"\n记录 {i} (ID: {record_id}):")
            try:
                # 解析JSON内容
                node_data = json.loads(content)
                print(f"  - text字段长度: {len(node_data.get('text', ''))}")
                print(f"  - text内容预览: {repr(node_data.get('text', '')[:100])}")
                print(f"  - embedding字段: {node_data.get('embedding')}")
                print(f"  - metadata: {node_data.get('metadata', {}).keys()}")
                print(f"  - class_name: {node_data.get('class_name')}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                print(f"  原始内容: {content[:200]}...")
        
        # 4. 统计text字段为空的记录数
        print("\n📈 统计分析:")
        cursor.execute("""
            SELECT COUNT(*) FROM embedding_metadata 
            WHERE key = '_node_content' 
            AND json_extract(string_value, '$.text') = ''
        """)
        empty_text_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM embedding_metadata 
            WHERE key = '_node_content' 
            AND json_extract(string_value, '$.embedding') IS NULL
        """)
        null_embedding_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM embedding_metadata WHERE key = '_node_content'")
        node_content_count = cursor.fetchone()[0]
        
        print(f"  - _node_content记录总数: {node_content_count}")
        print(f"  - text字段为空的记录数: {empty_text_count}")
        print(f"  - embedding字段为null的记录数: {null_embedding_count}")
        
        # 5. 查看embeddings表
        print("\n🔍 embeddings表信息:")
        cursor.execute("PRAGMA table_info(embeddings)")
        embedding_columns = cursor.fetchall()
        print("  embeddings表结构:")
        for col in embedding_columns:
            print(f"    - {col[1]} ({col[2]})")

        cursor.execute("SELECT COUNT(*) FROM embeddings")
        embeddings_count = cursor.fetchone()[0]
        print(f"  - embeddings表记录数: {embeddings_count}")

        if embeddings_count > 0:
            # 查看第一条记录的所有字段
            cursor.execute("SELECT * FROM embeddings LIMIT 1")
            sample_record = cursor.fetchone()
            if sample_record:
                print(f"  - 示例记录字段数: {len(sample_record)}")
                # 查看是否有embedding相关的字段
                for i, col in enumerate(embedding_columns):
                    col_name = col[1]
                    if 'embedding' in col_name.lower() or 'vector' in col_name.lower():
                        value = sample_record[i]
                        if value:
                            print(f"  - {col_name}字段长度: {len(str(value))} chars")
                        else:
                            print(f"  - {col_name}字段为空")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_embedding_metadata()
