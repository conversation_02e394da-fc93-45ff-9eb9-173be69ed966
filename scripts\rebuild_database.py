#!/usr/bin/env python3
"""
重建ChromaDB数据库的脚本
删除现有数据库并重新创建空的数据库结构
"""

import os
import shutil
import sys
import time
from pathlib import Path
import chromadb
from chromadb.config import Settings

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from backend.config.settings import Settings as AppSettings
    settings = AppSettings()
except ImportError:
    print("警告: 无法导入项目配置，使用默认配置")
    # 默认配置
    class DefaultSettings:
        chroma_persist_directory = "./storage"
        collection_name = "documents"
        embedding_model = "text-embedding-3-small"
    
    settings = DefaultSettings()

def backup_storage_directory():
    """备份现有存储目录"""
    storage_path = Path(settings.chroma_persist_directory)
    
    if storage_path.exists():
        backup_path = Path(f"{settings.chroma_persist_directory}_backup_{int(time.time())}")
        print(f"📦 备份现有存储目录到: {backup_path}")
        try:
            shutil.copytree(storage_path, backup_path)
            print(f"✅ 备份完成: {backup_path}")
            return str(backup_path)
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    else:
        print("📁 存储目录不存在，无需备份")
        return None

def delete_storage_directory():
    """删除现有存储目录"""
    storage_path = Path(settings.chroma_persist_directory)
    
    if storage_path.exists():
        print(f"🗑️  删除现有存储目录: {storage_path}")
        try:
            shutil.rmtree(storage_path)
            print("✅ 存储目录删除成功")
        except Exception as e:
            print(f"❌ 删除存储目录失败: {e}")
            return False
    else:
        print("📁 存储目录不存在")
    
    return True

def create_storage_directory():
    """创建存储目录"""
    storage_path = Path(settings.chroma_persist_directory)
    
    print(f"📁 创建存储目录: {storage_path}")
    try:
        storage_path.mkdir(parents=True, exist_ok=True)
        print("✅ 存储目录创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建存储目录失败: {e}")
        return False

def initialize_chromadb():
    """初始化ChromaDB数据库"""
    print("🔧 初始化ChromaDB数据库...")
    
    try:
        # 创建ChromaDB客户端
        client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory
        )
        print("✅ ChromaDB客户端创建成功")
        
        # 创建集合
        print(f"📚 创建集合: {settings.collection_name}")
        
        # 删除现有集合（如果存在）
        try:
            existing_collections = client.list_collections()
            for collection in existing_collections:
                if collection.name == settings.collection_name:
                    client.delete_collection(settings.collection_name)
                    print(f"🗑️  删除现有集合: {settings.collection_name}")
        except Exception as e:
            print(f"⚠️  检查现有集合时出错: {e}")
        
        # 创建新集合
        collection = client.create_collection(
            name=settings.collection_name,
            metadata={"description": "RAG文档向量存储集合"}
        )
        print(f"✅ 集合创建成功: {settings.collection_name}")
        
        # 验证集合
        collection_count = collection.count()
        print(f"📊 集合文档数量: {collection_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化ChromaDB失败: {e}")
        return False

def verify_database():
    """验证数据库是否正确创建"""
    print("🔍 验证数据库...")
    
    try:
        # 连接数据库
        client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory
        )
        
        # 检查集合
        collections = client.list_collections()
        print(f"📚 数据库中的集合数量: {len(collections)}")
        
        for collection in collections:
            print(f"  - 集合名称: {collection.name}")
            print(f"  - 文档数量: {collection.count()}")
        
        # 检查存储目录
        storage_path = Path(settings.chroma_persist_directory)
        if storage_path.exists():
            files = list(storage_path.rglob("*"))
            print(f"📁 存储目录文件数量: {len(files)}")
            
            # 显示主要文件
            for file_path in files:
                if file_path.is_file():
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    print(f"  - {file_path.name}: {size_mb:.2f}MB")
        
        print("✅ 数据库验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始重建ChromaDB数据库")
    print("=" * 50)
    
    # 显示配置信息
    print(f"📋 配置信息:")
    print(f"  - 存储目录: {settings.chroma_persist_directory}")
    print(f"  - 集合名称: {settings.collection_name}")
    print(f"  - 嵌入模型: {settings.embedding_model}")
    print()
    
    # 确认操作
    confirm = input("⚠️  此操作将删除现有数据库，是否继续? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    print()
    
    # 步骤1: 备份现有目录
    backup_path = backup_storage_directory()
    
    # 步骤2: 删除现有存储目录
    if not delete_storage_directory():
        print("❌ 重建失败：无法删除现有存储目录")
        return
    
    # 步骤3: 创建存储目录
    if not create_storage_directory():
        print("❌ 重建失败：无法创建存储目录")
        return
    
    # 步骤4: 初始化ChromaDB
    if not initialize_chromadb():
        print("❌ 重建失败：无法初始化ChromaDB")
        return
    
    # 步骤5: 验证数据库
    if not verify_database():
        print("❌ 重建失败：数据库验证失败")
        return
    
    print()
    print("🎉 数据库重建完成！")
    print("=" * 50)
    
    if backup_path:
        print(f"💾 备份文件位置: {backup_path}")
    
    print("📝 接下来的步骤:")
    print("  1. 重启应用服务")
    print("  2. 重新上传文档到data目录")
    print("  3. 点击'重新加载文档'按钮")

if __name__ == "__main__":
    main()
