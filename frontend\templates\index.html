<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RAG聊天应用</title>
    <link rel="stylesheet" href="/static/css/style.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container">
      <!-- 头部 -->
      <header class="header">
        <div class="header-content">
          <h1><i class="fas fa-robot"></i> RAG聊天助手</h1>
          <div class="header-actions">
            <a href="/documents" class="btn btn-primary" title="文档管理">
              <i class="fas fa-folder-open"></i> 文档管理
            </a>
            <button
              id="loadDocsBtn"
              class="btn btn-secondary"
              title="重新加载文档"
            >
              <i class="fas fa-sync-alt"></i> 加载文档
            </button>
            <button id="statusBtn" class="btn btn-info" title="查看系统状态">
              <i class="fas fa-info-circle"></i> 状态
            </button>
            <button id="clearBtn" class="btn btn-warning" title="清空对话历史">
              <i class="fas fa-trash"></i> 清空
            </button>
          </div>
        </div>
      </header>

      <!-- 主要内容区域 -->
      <main class="main-content">
        <!-- 聊天区域 -->
        <div class="chat-container">
          <div id="chatMessages" class="chat-messages">
            <!-- 欢迎消息 -->
            <div class="message assistant-message">
              <div class="message-avatar">
                <i class="fas fa-robot"></i>
              </div>
              <div class="message-content">
                <div class="message-text">
                  欢迎使用RAG聊天助手！我可以帮您回答基于已加载文档的问题。
                  <br /><br />
                  <strong>使用说明：</strong>
                  <ul>
                    <li>首先点击"加载文档"按钮加载data目录中的TXT文件</li>
                    <li>然后就可以向我提问了</li>
                    <li>我会基于文档内容为您提供准确的答案</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-container">
            <div class="input-wrapper">
              <textarea
                id="messageInput"
                placeholder="请输入您的问题..."
                rows="1"
                maxlength="1000"
              ></textarea>
              <button id="sendBtn" class="send-btn" title="发送消息">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
            <div class="input-info">
              <span id="charCount">0/1000</span>
              <span class="tip">按 Ctrl+Enter 发送</span>
            </div>
          </div>
        </div>
      </main>

      <!-- 状态栏 -->
      <footer class="status-bar">
        <div class="status-info">
          <span id="connectionStatus" class="status-item">
            <i class="fas fa-circle status-indicator"></i>
            <span>连接状态: 未知</span>
          </span>
          <span id="documentCount" class="status-item">
            <i class="fas fa-file-text"></i>
            <span>文档: 0</span>
          </span>
          <span id="storageSize" class="status-item">
            <i class="fas fa-database"></i>
            <span>存储: 0MB</span>
          </span>
        </div>
      </footer>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingOverlay" class="loading-overlay hidden">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p id="loadingText">处理中...</p>
      </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="modalTitle">标题</h3>
          <button id="modalClose" class="modal-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div id="modalContent">内容</div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/app.js"></script>
  </body>
</html>
