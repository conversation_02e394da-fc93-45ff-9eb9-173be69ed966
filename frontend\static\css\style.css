/* RAG聊天应用样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header h1 i {
    margin-right: 0.5rem;
    color: #fbbf24;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-info {
    background: #06b6d4;
    color: white;
}

.btn-info:hover {
    background: #0891b2;
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 2rem;
    scroll-behavior: smooth;
}

.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.assistant-message .message-avatar {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
}

.message-content {
    flex: 1;
    max-width: calc(100% - 56px);
}

.message-text {
    background: white;
    padding: 1rem 1.25rem;
    border-radius: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    line-height: 1.6;
}

.user-message .message-text {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 0.25rem;
}

.assistant-message .message-text {
    border-bottom-left-radius: 0.25rem;
}

.message-text ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-text li {
    margin: 0.25rem 0;
}

/* 消息源信息 */
.message-sources {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border-left: 4px solid #6366f1;
}

.message-sources h4 {
    font-size: 0.875rem;
    color: #4b5563;
    margin-bottom: 0.5rem;
}

.source-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-radius: 0.25rem;
    border: 1px solid #e5e7eb;
}

.source-filename {
    font-weight: 600;
    color: #6366f1;
    font-size: 0.875rem;
}

.source-content {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.source-score {
    font-size: 0.75rem;
    color: #9ca3af;
    float: right;
}

/* 输入区域 */
.input-container {
    padding: 1rem 2rem 1.5rem;
    background: white;
    border-top: 1px solid #e5e7eb;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 1rem;
    padding: 0.75rem;
    transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    min-height: 24px;
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.input-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.tip {
    color: #9ca3af;
}

/* 状态栏 */
.status-bar {
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    padding: 0.75rem 2rem;
}

.status-info {
    display: flex;
    gap: 2rem;
    font-size: 0.875rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
}

.status-indicator {
    font-size: 0.5rem;
}

.status-indicator.connected {
    color: #10b981;
}

.status-indicator.disconnected {
    color: #ef4444;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 2rem;
    color: #6366f1;
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: #6b7280;
    font-weight: 500;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 1rem;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #374151;
}

.modal-body {
    padding: 2rem;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

/* 工具类 */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: #10b981;
}

.text-error {
    color: #ef4444;
}

.text-warning {
    color: #f59e0b;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        height: 100vh;
        border-radius: 0;
    }
    
    .header {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .chat-messages {
        padding: 1rem;
    }
    
    .input-container {
        padding: 1rem;
    }
    
    .status-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}
