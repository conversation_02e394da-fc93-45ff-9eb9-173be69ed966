#!/usr/bin/env python3
"""
详细检查ChromaDB中documents字段的具体位置
"""
import sqlite3
import json
from pathlib import Path
import chromadb

def check_chromadb_documents():
    """检查ChromaDB中documents字段的具体位置"""
    db_path = Path("storage/chroma.sqlite3")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        # 1. 通过ChromaDB API查看
        print("🔍 通过ChromaDB API查看documents:")
        client = chromadb.PersistentClient(path="storage")
        collection = client.get_collection("documents")
        
        # 获取数据，包含documents字段
        result = collection.get(
            limit=3,
            include=["documents", "metadatas", "embeddings"]
        )
        
        print(f"  - 通过API获取到 {len(result['ids'])} 条记录")
        for i, doc_id in enumerate(result['ids']):
            document = result['documents'][i] if result['documents'] else None
            metadata = result['metadatas'][i] if result['metadatas'] else {}
            
            print(f"\n  记录 {i+1} (ID: {doc_id[:8]}...):")
            print(f"    - documents字段长度: {len(document) if document else 0}")
            print(f"    - documents内容预览: {document[:100] if document else 'None'}...")
            print(f"    - 文件名: {metadata.get('filename', 'N/A')}")
        
        print("\n" + "="*60)
        
        # 2. 直接查看SQLite数据库结构
        print("🔍 直接查看SQLite数据库结构:")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"  - 数据库中的表: {tables}")
        
        # 查看每个表的结构，寻找documents相关字段
        for table in tables:
            print(f"\n📋 表 '{table}' 的结构:")
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            has_document_field = False
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                print(f"    - {col_name} ({col_type})")
                if 'document' in col_name.lower():
                    has_document_field = True
            
            if has_document_field:
                print(f"    ⭐ 表 '{table}' 包含documents相关字段")
                
                # 查看这个表的数据
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    - 记录数: {count}")
                
                if count > 0:
                    # 查看前几条记录
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    records = cursor.fetchall()
                    for i, record in enumerate(records):
                        print(f"    - 记录{i+1}: {record[:3]}...")  # 只显示前3个字段
        
        print("\n" + "="*60)
        
        # 3. 特别检查embedding_fulltext_search相关表
        print("🔍 检查全文搜索相关表:")
        fts_tables = [t for t in tables if 'fulltext' in t.lower()]
        for table in fts_tables:
            print(f"\n📋 表 '{table}':")
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  - 记录数: {count}")
            
            if count > 0 and 'content' in table:
                cursor.execute(f"SELECT * FROM {table} LIMIT 2")
                records = cursor.fetchall()
                for i, record in enumerate(records):
                    print(f"  - 记录{i+1}: {record}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

def check_chromadb_internal_storage():
    """检查ChromaDB内部存储机制"""
    try:
        print("\n🔍 检查ChromaDB内部存储机制:")
        client = chromadb.PersistentClient(path="storage")
        collection = client.get_collection("documents")
        
        # 获取collection的详细信息
        print(f"  - Collection名称: {collection.name}")
        print(f"  - Collection记录数: {collection.count()}")
        
        # 尝试获取不同的include参数
        print("\n📊 测试不同的include参数:")
        
        # 只获取IDs
        result_ids = collection.get(limit=1, include=[])
        print(f"  - 只获取IDs: {len(result_ids['ids'])} 条")
        
        # 获取documents
        result_docs = collection.get(limit=1, include=["documents"])
        print(f"  - 包含documents: {len(result_docs.get('documents', []))} 条")
        if result_docs.get('documents'):
            doc = result_docs['documents'][0]
            print(f"    - 第一个document长度: {len(doc) if doc else 0}")
        
        # 获取metadatas
        result_meta = collection.get(limit=1, include=["metadatas"])
        print(f"  - 包含metadatas: {len(result_meta.get('metadatas', []))} 条")
        
        # 获取embeddings
        result_embed = collection.get(limit=1, include=["embeddings"])
        print(f"  - 包含embeddings: {len(result_embed.get('embeddings', []))} 条")
        if result_embed.get('embeddings'):
            embed = result_embed['embeddings'][0]
            print(f"    - 第一个embedding长度: {len(embed) if embed else 0}")
        
    except Exception as e:
        print(f"❌ 检查ChromaDB内部存储失败: {e}")

if __name__ == "__main__":
    print("🚀 开始检查ChromaDB中documents字段的具体位置")
    print("=" * 60)
    
    check_chromadb_documents()
    check_chromadb_internal_storage()
